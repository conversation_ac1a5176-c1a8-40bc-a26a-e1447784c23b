﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading;
using UniAuto.UniBCS.Core;
using UniAuto.UniBCS.Core.Generic;
using UniAuto.UniBCS.Core.Message;
using UniAuto.UniBCS.Entity;
using UniAuto.UniBCS.EntityManager;
using UniAuto.UniBCS.MISC;
//using UniAuto.UniBCS.OpiSpec;
using UniAuto.UniBCS.PLCAgent.PLC;


namespace UniAuto.UniBCS.INX.BCSService {
    public partial class PLCService : PLCServiceBase {

        #region 1. Glass ID Inquire (EHC06) / Glass ID Inquire for Titler
        public void EHC06GlassIDInquire(Trx _inputData) {
            string _s = string.Empty;
            Job _job = null;
            Equipment _eqp = null;
            bool _isReqBitClear = false;
            eOnOff _triggerBit = eOnOff.OFF;

            Trx _outputData = GetPLCSyncReadTrx(_inputData.Metadata.NodeNo, Globals.TrxName.PLC.MTL.HHC06GlassIDInquireReply);
            if (_outputData == null)
            {
                _s = string.Empty;
                _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Send);
                _s += "BCS NOT FOUND OUTPUTDATA OBJECT.";
                this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                return;
            }

            _outputData.ClearTrxWith0();

            try
            {
                WriteEvtTrxLog(_inputData, MethodBase.GetCurrentMethod().Name);
                if (_inputData.IsInitTrigger) return;
                
                //註解 : CF8A-C#Migration - modify                        
                RawData_EHC06GlassIDInquire EHC06 = new RawData_EHC06GlassIDInquire(_inputData);
                

                _triggerBit = (eOnOff)int.Parse(_inputData.EventGroups[0].Events[1].Items[0].Value);
                _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                //註解 : CF8A-C#Migration - mark
                //_s += string.Format("GlassNumber<{0}>, EHC06GlassIDInquireRequest_Bit<{1}>.", _inputData.EventGroups[0].Events[0].Items["GlassNumber"].Value.ToUpperTrim(), _triggerBit);
                //註解 : CF8A-C#Migration - modify
                _s += string.Format("GlassNumber<{0}>, EHC06GlassIDInquireRequest_Bit<{1}>.", EHC06._raw_glassno, _triggerBit);
  
                this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);

                int itemCount = 0;
                if (_inputData.EventGroups != null &&
                    _inputData.EventGroups.Count > 0 &&
                    _inputData.EventGroups[0].Events != null &&
                    _inputData.EventGroups[0].Events.Count > 0 &&
                    _inputData.EventGroups[0].Events[0] != null &&
                    _inputData.EventGroups[0].Events[0].Items != null)
                {
                    itemCount = _inputData.EventGroups[0].Events[0].Items.Count;
                }

                _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                _s += string.Format("EHC35 Events[0].Items Count: {0}", itemCount);
                this.Logger.LogDebugWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);

                //[Start]v1.0.0.107-1
                if(_triggerBit == eOnOff.ON)
                {
                    ObjectManager.HSInfoManager.AddHSInfoEntity(new HSInfoEntity(_inputData.Metadata.NodeNo,
                                                                                  _inputData.Name,
                                                                                  _inputData.TrackKey,
                                                                                  _inputData.EventGroups[0].Events[0],
                                                                                  0,
                                                                                  0));
                }
                else
                {
                    ObjectManager.HSInfoManager.DeleteHSInfoEntity(_inputData.Name);
                }
                //[End]v1.0.0.107-1

                if (_triggerBit == eOnOff.ON)
                {
                    #region Signal ON logic...
                    _eqp = ObjectManager.EquipmentManager.GetEQP(_inputData.Metadata.NodeNo);
                    if (_eqp == null)
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                        _s += "BCS NOT FOUND EQUIPMENT OBJECT.";
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    }
                    //註解 : CF8A-C#Migration - mark
                    //_job = ObjectManager.JobManager.GetJobByNo(_inputData.EventGroups[0].Events[0].Items["GlassNumber"].Value.ToUpperTrim());
                    //註解 : CF8A-C#Migration - modigy
                    _job = ObjectManager.JobManager.GetJobByNo(EHC06._raw_glassno);
                    if (_job == null)
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                        //註解 : CF8A-C#Migration - mark
                        //_s += string.Format("GlassNo<{0}>, No such Glass in WIP.", _inputData.EventGroups[0].Events[0].Items["GlassNumber"].Value.ToUpperTrim());
                        //註解 : CF8A-C#Migration - modigy
                        _s += string.Format("GlassNo<{0}>, No such Glass in WIP.", EHC06._raw_glassno);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);

                        _outputData.EventGroups[0].Events[0].Items[1].Value = "2"; //NG, Null

                        //註解 : CF8A-C#Migration - EHC06GlassIDInquire
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                        _s += string.Format("GlassID<{0}>, ReturnCode<{1}>.", _outputData.EventGroups[0].Events[0].Items[0].Value, _outputData.EventGroups[0].Events[0].Items[1].Value);
                        this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);

                    }
                    else
                    {
                        if (_eqp != null && _eqp.Data.NODEATTRIBUTE == "TTL") //TTL=Titler
                        {
                            if (!ParameterManager["S_SKIP_GLASS_ID_DUPLICATE_CHECK"].GetBoolean()) //default:: skip this function!! (true)
                            {
                                //ENABLED BCS's GlassID Duplicate Check function!!
                                if (_job._GlassID._TitlerHasRequested) //Titler已經有要求過了!!
                                {
                                    if (_job._GlassID._TitlerRequestTimeInterval < ParameterManager["TJ"].GetInteger()) //TJ=86400s (1d) (default)
                                    {
                                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                                        _s += string.Format("GlassNo<{0}> found, but Titler request again! (3,NG, Duplicated)", _inputData.EventGroups[0].Events[0].Items["GlassNumber"].Value.ToUpperTrim());
                                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);

                                        _outputData.ClearTrxWith0();
                                        _outputData.EventGroups[0].Events[0].Items[1].Value = "3"; //NG, Duplicated
                                    }
                                }
                            }


                            if (_outputData.EventGroups[0].Events[0].Items[1].Value != "3")
                            {
                                lock (_job)
                                {
                                    _job._GlassID._TitlerHasRequested = true; //如果已經為true再次設定true, 主要是為了更新Request Time!!

                                    ObjectManager.JobManager.EnqueueSave(_job);
                                }

                                switch (_job._GlassID._BCSGlassID.Substring(0, 3))
                                {
                                    case "BCM":
                                    case "GID":
                                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                                        _s += string.Format("GlassNo<{0}>) found, but Glass ID=[{1}].",
                                            _inputData.EventGroups[0].Events[0].Items["GlassNumber"].Value.ToUpperTrim(),
                                            _job._GlassID._BCSGlassID);
                                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);

                                        _outputData.ClearTrxWith0();
                                        _outputData.EventGroups[0].Events[0].Items[1].Value = "2"; //NG, Null
                                        break;
                                    default:
                                        _outputData.EventGroups[0].Events[0].Items[0].Value = _job._GlassID._BCSGlassID;
                                        _outputData.EventGroups[0].Events[0].Items[1].Value = "1"; //OK
                                        break;
                                }
                            }
                        }
                        else
                        {
                            _outputData.EventGroups[0].Events[0].Items[0].Value = _job._GlassID._BCSGlassID;
                            _outputData.EventGroups[0].Events[0].Items[1].Value = "1"; //OK
                            //_outputData.EventGroups[0].Events[0].OpDelayTimeMS = ParameterManager["EVENTDELAYTIME"].GetInteger(); //廷遲寫入Word / Command...相關信息 Word 區塊!!
                        }



                    }
                    #endregion
                    //註解 : CF8A-C#Migration - EHC06GlassIDInquire

                    if (_outputData.EventGroups[0].Events[0].Items[1].Value == "1")
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                        _s += string.Format("GlassID<{0}>, ReturnCode<{1}>.", _outputData.EventGroups[0].Events[0].Items[0].Value, _outputData.EventGroups[0].Events[0].Items[1].Value);
                        this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    }
                    else
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                        _s += string.Format("ReturnCode<{0}>.", _outputData.EventGroups[0].Events[0].Items[1].Value);
                        this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    }
                    //註解 : CF8A-C#Migration - mark 
                    //_s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                    //_s += string.Format("GlassID<{0}>, ReturnCode<{1}>.", _outputData.EventGroups[0].Events[0].Items[0].Value, _outputData.EventGroups[0].Events[0].Items[1].Value);
                    //this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                }
            }
            catch (Exception ex)
            {
                LogError(MethodBase.GetCurrentMethod().Name + "()", ex);
            }
            finally
            {
                //[Start] v1.0.0.24-7
                if (!_outputData.NullOrEmpty())
                {
                    if (_triggerBit == eOnOff.ON)
                    {
                        string[] _temp = _inputData.Name.Split('_').ToArray();
                        if (!_temp.NullOrEmpty() && _temp.Count<string>() >= 2)
                        {
                            string _name = _temp[1];
                            Trx _trx = GetPLCSyncReadTrx(_inputData.Metadata.NodeNo, _name);
                            if (!_trx.NullOrEmpty())
                            {
                                if ((eOnOff)_trx.EventGroups[0].Events[1].Items[0].Value.Parse() == eOnOff.OFF)
                                {
                                    _outputData.ClearTrxWith0();
                                    _isReqBitClear = true;

                                    _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                                    _s += string.Format("Request Bit Has Clear.");
                                    //this.Logger.LogErrorWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);//v1.0.0.183-11
                                    this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);//v1.0.0.183-11
                                }
                            }
                            else
                            {
                                _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                                _s += Helper.GetDesc(Globals.ReturnCode.Common.RC_TRX_NOT_FOUND, new object[] { string.Format("{0}_{1}", _inputData.Metadata.NodeNo, _name) }, true);
                                this.Logger.LogErrorWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", string.Format("{0}", _s));

                                _outputData.ClearTrxWith0();
                            }
                        }
                        else
                        {
                            _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                            _s += Helper.GetDesc(Globals.ReturnCode.Common.RC_TRX_NOT_FOUND, new object[] { string.Format("{0}_{1}", _inputData.Metadata.NodeNo, string.Empty) }, true);
                            this.Logger.LogErrorWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", string.Format("{0}", _s));

                            _outputData.ClearTrxWith0();

                        }
                    }

                    //[Start]v1.0.0.107-1
                    if (ObjectManager.HSInfoManager.IsHSValid(_inputData, _inputData.EventGroups[0].Events[0], 0, 0, _triggerBit))
                    {
                        SendPLCData(_outputData, _inputData.TrackKey);
                    }

                    if (_triggerBit == eOnOff.ON)
                    {
                        if (_timerManager.IsAliveTimer(string.Format("{0}", _outputData.Name.Trim()))) _timerManager.TerminateTimer(string.Format("{0}", _outputData.Name.Trim()));
                        _timerManager.CreateTimer(string.Format("{0}", _outputData.Name.Trim()),
                            false,
                            //(ParameterManager["S_BC_ACTIVE_TRIGGER_TIME_OUT"].GetInteger() * 1000),
                            ParameterManager["T1"].GetInteger(), //6000 (6s)
                            new System.Timers.ElapsedEventHandler(EHC06GlassIDInquire_OFF),
                            new object[] { _outputData.Metadata.NodeNo, _outputData.Name.Trim(), _inputData.TrackKey });
                    }
                    //[End]v1.0.0.107-1

                    if ((_triggerBit == eOnOff.ON) && (!_isReqBitClear))
                    {
                        //TODO:: Add History Information
                        //註解 : CF8A-C#Migration - mark
                        //ObjectManager.HistoryManager.Record_SHIS_GlassIDInquire(_eqp, _job, _inputData.EventGroups[0].Events[0].Items["GlassNumber"].Value.ToUpperTrim(), _outputData.EventGroups[0].Events[0].Items[0].Value, _outputData.EventGroups[0].Events[0].Items[1].Value); //v1.0.0.8-7//v1.0.0.183-14
                        //註解 : CF8A-C#Migration - modify
                        RawData_EHC06GlassIDInquire EHC06 = new RawData_EHC06GlassIDInquire(_inputData);           
                        ObjectManager.HistoryManager.Record_SHIS_GlassIDInquire(_eqp, _job, EHC06._raw_glassno, _outputData.EventGroups[0].Events[0].Items[0].Value, _outputData.EventGroups[0].Events[0].Items[1].Value);
              
                    
                    }
                }
            }
            //[End] v1.0.0.24-7
        }

        //[Start]v1.0.0.107-1
        void EHC06GlassIDInquire_OFF(object subjet, System.Timers.ElapsedEventArgs e)
        {
            string _s = string.Empty;
            string _ss = string.Empty;
            try
            {
                UserTimer timer = subjet as UserTimer;
                object[] _objs = (object[])timer.State;

                string _nodeNo = (string)_objs[0];
                string _trxName = (string)_objs[1];
                string _trcaeKey = (string)_objs[2];

                _ss = Helper.GetLogTitle(_nodeNo, _trcaeKey);

                Trx _trx = GetPLCSyncReadTrx(_nodeNo, _trxName.Replace(string.Format("{0}_", _nodeNo), ""));
                if (_trx.NullOrEmpty())
                {
                    _s = Helper.GetDesc(Globals.ReturnCode.Common.RC_TRX_NOT_FOUND, new object[] { string.Format("{0}_{1}", _nodeNo, _trxName.Replace(string.Format("{0}_", _nodeNo), "")) }, true);
                    this.Logger.LogErrorWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", string.Format("{0}{1}", _ss, _s));
                    return;
                }

                if ((_trx.EventGroups[0].Events[0].Items[0].Value.Trim() != string.Empty) || (_trx.EventGroups[0].Events[0].Items[1].Value != "0"))
                {
                    _trx.ClearTrxWith0();
                    SendPLCData(_trx, _trcaeKey);
                    _s = string.Format("TrxName<{0}>, TraceKey<{1}>: The reply Data Is Exist, So it will clear.", _trxName, _trcaeKey);
                    this.Logger.LogErrorWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", string.Format("{0}{1}", _ss, _s));
                }
            }
            catch (Exception ex)
            {
                LogError(MethodBase.GetCurrentMethod().Name + "()", ex);
            }
        }
        //[End]v1.0.0.107-1
        #endregion
        #region 2. Glass Data Inquire/Create/Restore (EHC02) [for Glass Data Loss]
        public void EHC02GlassDataInquire(Trx _inputData) {
            //[Start]v1.0.0.18-2
            string _s = string.Empty;
            Trx _outputData = null;
            Cassette _cst = null;
            bool isUnknowReq = false;
            eOnOff _triggerBit = eOnOff.OFF;
            Equipment _eqp = null;
            Job job = null;
            Line _line = null;
            bool _isReqBitClear = false;
            string _GlsNoHex = string.Empty; //v1.0.0.70-1

            try
            {
                WriteEvtTrxLog(_inputData, MethodBase.GetCurrentMethod().Name);

                if (_inputData.IsInitTrigger) return;
                //註解 : CF8A-C#Migration - mark
                //string _sGlassID = _inputData.EventGroups[0].Events[0].Items["GlassID"].Value.ToString();
                //string _sGlassNumber = _inputData.EventGroups[0].Events[0].Items["GlassNumber"].Value.ToString();

                //註解 : CF8A-C#Migration - modify
                RawData_EHC02GlassDataInquire EHC02 = new RawData_EHC02GlassDataInquire(_inputData);
                string _sGlassID = EHC02._raw_sGlassID;
                string _sGlassNumber = EHC02._raw_sGlassNumber;

                _triggerBit = (eOnOff)int.Parse(_inputData.EventGroups[0].Events[1].Items[0].Value);

                //[Start]v1.0.0.107-1
                if (_triggerBit == eOnOff.ON)
                {
                    ObjectManager.HSInfoManager.AddHSInfoEntity(new HSInfoEntity(_inputData.Metadata.NodeNo,
                                                                                  _inputData.Name,
                                                                                  _inputData.TrackKey,
                                                                                  _inputData.EventGroups[0].Events[0],
                                                                                  0,
                                                                                  1));
                }
                else
                {
                    ObjectManager.HSInfoManager.DeleteHSInfoEntity(_inputData.Name);
                }
                //[End]v1.0.0.107-1

                _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                _s += string.Format("GlassID<{0}>, GlassNumber<{1}>, EHC02GlassDataInquireRequest_Bit<{2}>.", _sGlassID, _sGlassNumber, _triggerBit);
                this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);

                _outputData = GetPLCSyncReadTrx(_inputData.Metadata.NodeNo, Globals.TrxName.PLC.MTL.HHC02GlassDataInquireReply);
                _outputData.ClearTrxWith0();

                if (_triggerBit == eOnOff.ON)
                {
                    _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Receive);
                    _s += string.Format("GlassID[{0}] Glass No[{1}])", _sGlassID, _sGlassNumber);
                    this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);

                    //寫入PLC-> HHC02 Glass Data Inquire Glass ID Reply
                    //Trx outputData = GetServerAgent(Globals.AgentName.PLCAgent).GetTransactionFormat(string.Format("{0}_{1}", _inputData.Metadata.NodeNo, Globals.TrxName.PLC.MTL.HHC02GlassDataInquireReply)) as Trx;

                    string ReturnCode = string.Empty;

                    _eqp = ObjectManager.EquipmentManager.GetEQP(_inputData.Metadata.NodeNo);
                    if (_eqp == null)
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Receive);
                        _s += Helper.GetDesc(Globals.ReturnCode.Common.RC_EQP_NOT_FOUND, new object[] { _inputData.Metadata.NodeNo }, true);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                        return;
                    }

                    _line = ObjectManager.LineManager.GetLine(Workbench.ServerName);
                    if (_line == null)
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Receive);
                        _s += Helper.GetDesc(Globals.ReturnCode.Common.RC_LINE_NOT_FOUND, new object[] { Workbench.ServerName }, true);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                        return;
                    }

                    if (_eqp.File._EqpControlMode == eEQPControlMode.OFFLINE_MODE)
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                        _s += string.Format("EQP<{0}> is Offline, BCS skip.", _inputData.Metadata.NodeNo);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    }

                    if (_eqp.File._Status.EQPStatus != eEQPStatus.STOP)
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                        _s += string.Format("EQP<{0}> Status isn't Stop.", _inputData.Metadata.NodeNo);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    }

                    //[Start]v1.0.0.1-4
                    if (_sGlassID.Trim() == string.Empty && _sGlassNumber.Trim() == "0000")
                    {
                        //v1.0.0.70-1
                        //job = _eqp.File._LastReceivedJob;//machine last received glass
                        _GlsNoHex = JobExt.GlassNumberHex(0, _line.Data.LINENO, _line.File._UnknowIDSlotSerial);
                        job = ObjectManager.JobManager.CreateJob(_GlsNoHex);
                        job = job.Copy(_eqp.File._LastReceivedJob);
                        job._JobData._GlassNumber = _GlsNoHex;

                        isUnknowReq = true;

                        _line.AddUnknowSerial();
                        _line.AddUnknowGlassSerial();

                        ObjectManager.LineManager.EnqueueSave(_line.File);
                        if (job != null)
                        {
                            _cst = job._SourceCassette.NullOrEmpty() ? null : job._SourceCassette;//v1.0.0.9-16
                            if (_cst == null)
                            {
                                _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Receive);
                                _s += _s += MISC.Helper.GetDesc(Globals.ReturnCode.Common.RC_CST_NOT_FOUND_BY_ID, new object[] { job._SourceCassette.NullOrEmpty() ? string.Empty : job._SourceCassette._CassetteID }, true);
                                this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                            }
                        }
                    }
                    else
                    {
                        if ((_sGlassID.Trim() != string.Empty) && (_sGlassNumber.Trim() != "0000"))
                        {
                            job = ObjectManager.JobManager.GetJobByNo(_sGlassNumber);
                        }
                        else if (_sGlassID.Trim() != string.Empty)
                        {
                            job = ObjectManager.JobManager.GetJobByID(_sGlassID);
                        }
                        else if (_sGlassNumber.Trim() != "0000")
                        {
                            job = ObjectManager.JobManager.GetJobByNo(_sGlassNumber);
                        }

                        if (job != null)
                        {
                            _cst = job._SourceCassette.NullOrEmpty() ? null : job._SourceCassette;//v1.0.0.9-16
                        }

                        //[Start]v1.0.0.117-8
                        if(job.NullOrEmpty())
                        {
                            _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Receive);
                            _s += string.Format("Can not find glass wip. Glass Number<[0]>, GlassID<{1}>.", _sGlassNumber, _sGlassID);
                            this.Logger.LogErrorWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                        }
                        //[End]v1.0.0.117-8

                        if (_cst == null)
                        {
                            _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Receive);
                            _s += _s += MISC.Helper.GetDesc(Globals.ReturnCode.Common.RC_CST_NOT_FOUND_BY_ID, new object[] { (job.NullOrEmpty() || job._SourceCassette.NullOrEmpty()) ? string.Empty : job._SourceCassette._CassetteID }, true); //v1.0.0.16-2
                            this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                        }
                    }

                    // 找到wip,補上JobData資料
                    if (!isUnknowReq)
                    {
                        if (job != null)
                        {
                            //Job2Trx(_outputData.Metadata.NodeNo, job._SourceCassette.NullOrEmpty() ? null : job._SourceCassette, job, ref _outputData);
                            Job2TrxForGlassDataInquire(_outputData.Metadata.NodeNo, job._SourceCassette.NullOrEmpty() ? null : job._SourceCassette, job, ref _outputData); //********-5
                            //_outputData.EventGroups[0].Events[0].RawData = job.ToRawData(); //v1.0.0.56-6


                            _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                            _s += string.Format("Current RemoveFlag<{0}>.", job.RemoveFlag);
                            this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);

                            if (job.RemoveFlag)
                            {
                                _outputData.EventGroups[0].Events[1].Items[0].Value = "1";//OK
                            }
                            else
                            {
                                if (job.CurrentEQPNo == _inputData.Metadata.NodeNo)
                                {
                                    _outputData.EventGroups[0].Events[1].Items[0].Value = "1";//OK
                                }
                                else
                                {
                                    _outputData.EventGroups[0].Events[1].Items[0].Value = "3";//Duplicated
                                }
                            }
                        }
                        else
                        {
                            _outputData.EventGroups[0].Events[1].Items[0].Value = "2";//NG
                        }
                    }
                    else
                    {
                        if (!job.NullOrEmpty())
                        {
                            //v1.0.0.70-1
                            Job2TrxForUnknowGlassDataInquire(_outputData.Metadata.NodeNo, _cst, job, _GlsNoHex, ref _outputData);

                            if (job.CurrentEQPNo == _inputData.Metadata.NodeNo)
                            {
                                _outputData.EventGroups[0].Events[1].Items[0].Value = "1";//OK
                            }
                            else
                            {
                                _outputData.EventGroups[0].Events[1].Items[0].Value = "3";//Duplicated
                            }
                        }
                        else
                        {
                            _outputData.EventGroups[0].Events[1].Items[0].Value = "2";//NG
                        }
                    }

                    //註解 : CF8A-C#Migration - EHC02GlassDataInquire
                    if (_outputData.EventGroups[0].Events[1].Items[0].Value == "1")
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                        _s += string.Format("Result: GlassID<{0}>, GlassNumber<{1}>, Return<{2}>.", _sGlassID, _sGlassNumber, _outputData.EventGroups[0].Events[1].Items[0].Value);
                        //_s += string.Format("Result: GlassID<{0}>, GlassNumber<{1}>, Return<{2}>.", _outputData.EventGroups[0].Events[0].Items["GlassID"].Value, _outputData.EventGroups[0].Events[0].Items["GlassNumber"].Value, _outputData.EventGroups[0].Events[1].Items[0].Value);
                        this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    }
                    else
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                        _s += string.Format("Result: Return<{0}>.", _outputData.EventGroups[0].Events[1].Items[0].Value);
                        //_s += string.Format("Result: GlassID<{0}>, GlassNumber<{1}>, Return<{2}>.", _outputData.EventGroups[0].Events[0].Items["GlassID"].Value, _outputData.EventGroups[0].Events[0].Items["GlassNumber"].Value, _outputData.EventGroups[0].Events[1].Items[0].Value);
                        this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    }

                    //註解 : CF8A-C#Migration - mark
                    //_s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                    //_s += string.Format("Result: GlassID<{0}>, GlassNumber<{1}>, Return<{2}>.", _outputData.EventGroups[0].Events[0].Items["GlassID"].Value, _outputData.EventGroups[0].Events[0].Items["GlassNumber"].Value, _outputData.EventGroups[0].Events[1].Items[0].Value);
                    //this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);

                    //[End]v1.0.0.1-4
                }
            }
            catch (Exception ex)
            {
                LogError(MethodBase.GetCurrentMethod().Name + "()", ex);
            }
            finally
            {
                //[Start] v1.0.0.24-7
                if (!_outputData.NullOrEmpty())
                {
                    if (_triggerBit == eOnOff.ON)
                    {
                        Trx _trx = GetPLCSyncReadTrx(_inputData.Metadata.NodeNo, Globals.TrxName.PLC.LTM.EHC02GlassDataInquire);
                        if (!_trx.NullOrEmpty())
                        {
                            if ((eOnOff)_trx.EventGroups[0].Events[1].Items[0].Value.Parse() == eOnOff.OFF)
                            {
                                _outputData.ClearTrxWith0();
                                _isReqBitClear = true;

                                _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                                _s += string.Format("Request Bit Has Clear.");
                                //this.Logger.LogErrorWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);//v1.0.0.183-11
                                this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);//v1.0.0.183-11
                            }
                        }
                        else
                        {
                            _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                            _s += Helper.GetDesc(Globals.ReturnCode.Common.RC_TRX_NOT_FOUND, new object[] { string.Format("{0}_{1}", _inputData.Metadata.NodeNo, Globals.TrxName.PLC.LTM.EHC02GlassDataInquire) }, true);
                            this.Logger.LogErrorWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", string.Format("{0}", _s));

                            _outputData.ClearTrxWith0();
                        }
                    }

                    //[Start]v1.0.0.107-1
                    if (ObjectManager.HSInfoManager.IsHSValid(_inputData, _inputData.EventGroups[0].Events[0], 0, 1, _triggerBit))
                    {
                        SendPLCData(_outputData, _inputData.TrackKey);
                    }

                    if (_triggerBit == eOnOff.ON)
                    {
                        if (_timerManager.IsAliveTimer(string.Format("{0}", _outputData.Name.Trim()))) _timerManager.TerminateTimer(string.Format("{0}", _outputData.Name.Trim()));
                        _timerManager.CreateTimer(string.Format("{0}", _outputData.Name.Trim()),
                            false,
                            //(ParameterManager["S_BC_ACTIVE_TRIGGER_TIME_OUT"].GetInteger() * 1000),
                            ParameterManager["T1"].GetInteger(), //6000 (6s)
                            new System.Timers.ElapsedEventHandler(EHC02GlassDataInquire_OFF),
                            new object[] { _outputData.Metadata.NodeNo, _outputData.Name.Trim(), _inputData.TrackKey });
                    }
                    //[End]v1.0.0.107-1

                    if ((_triggerBit == eOnOff.ON) && (!_isReqBitClear))
                    {
                        //[Start]v1.0.0.117-8
                        if (!job.NullOrEmpty())
                        {
                            ObjectManager.HistoryManager.Record_SHIS_GlassDataInquire(_eqp, _line, job, MethodBase.GetCurrentMethod().Name, _outputData, _inputData.TrackKey);
                        }
                        else
                        {
                            ObjectManager.HistoryManager.Record_SHIS_GlassDataInquire(_eqp, _line, MethodBase.GetCurrentMethod().Name, _outputData, _inputData.TrackKey);
                        }
                        //[End]v1.0.0.117-8
                    }
                }
                //[End] v1.0.0.24-7
            }
            //[End]v1.0.0.18-2
        }

        //[Start]v1.0.0.107-1
        void EHC02GlassDataInquire_OFF(object subjet, System.Timers.ElapsedEventArgs e)
        {
            string _s = string.Empty;
            string _ss = string.Empty;
            try
            {
                UserTimer timer = subjet as UserTimer;
                object[] _objs = (object[])timer.State;

                string _nodeNo = (string)_objs[0];
                string _trxName = (string)_objs[1];
                string _trcaeKey = (string)_objs[2];

                _ss = Helper.GetLogTitle(_nodeNo, _trcaeKey);

                Trx _trx = GetPLCSyncReadTrx(_nodeNo, _trxName.Replace(string.Format("{0}_", _nodeNo), ""));
                if (_trx.NullOrEmpty())
                {
                    _s = Helper.GetDesc(Globals.ReturnCode.Common.RC_TRX_NOT_FOUND, new object[] { string.Format("{0}_{1}", _nodeNo, _trxName.Replace(string.Format("{0}_", _nodeNo), "")) }, true);
                    this.Logger.LogErrorWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", string.Format("{0}{1}", _ss, _s));
                    return;
                }

                if (_trx.EventGroups[0].Events[1].Items[0].Value != "0")
                {
                    _trx.ClearTrxWith0();
                    SendPLCData(_trx, _trcaeKey);
                    _s = string.Format("TrxName<{0}>, TraceKey<{1}>: The reply Data Is Exist, So it will clear.", _trxName, _trcaeKey);
                    this.Logger.LogErrorWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", string.Format("{0}{1}", _ss, _s));
                }
            }
            catch (Exception ex)
            {
                LogError(MethodBase.GetCurrentMethod().Name + "()", ex);
            }
        }
        //[End]v1.0.0.107-1

        public void EHC02GlassDataInquireGlassID(Trx _inputData) {
        }
        #endregion
        #region 3. Glass Data Register (EHC03)
        public void EHC03GlassDataRegisterReport(Trx _inputData) {
            string _s = string.Empty;
            eOnOff _triggerBit = eOnOff.OFF;
            string _raw = string.Empty;
            try {
                WriteEvtTrxLog(_inputData, MethodBase.GetCurrentMethod().Name);

                if (_inputData.IsInitTrigger) return;
                //註解 : CF8A-C#Migration - mark
                //string _sGlassNumber = _inputData.EventGroups[0].Events[0].Items["GlassNumber"].Value.ToUpperTrim();
                //string _sGlassID = _inputData.EventGroups[0].Events[0].Items["GlassID"].Value.ToUpperTrim();

                //註解 : CF8A-C#Migration - modify
                RawData_EHC03GlassDataRegisterReport EHC03 = new RawData_EHC03GlassDataRegisterReport(_inputData);
                string _sGlassNumber = EHC03._raw_sGlassNumber;
                string _sGlassID = string.Empty;

                bool IsWipUpData = true;

                _triggerBit = (eOnOff)int.Parse(_inputData.EventGroups[0].Events[1].Items[0].Value);

                _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                _s += string.Format("GlassID<{0}>, GlassNumber<{1}>, EHC03GlassDataRegisterRequest_Bit<{2}>.", _sGlassNumber, _sGlassID, _triggerBit);
                this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);

                if (_triggerBit == eOnOff.ON) {
                    
                    Equipment _eqp = ObjectManager.EquipmentManager.GetEQP(_inputData.Metadata.NodeNo);
                    if (_eqp == null)
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Receive);
                        _s += Helper.GetDesc(Globals.ReturnCode.Common.RC_EQP_NOT_FOUND, new object[] { _inputData.Metadata.NodeNo }, true);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                        return;
                    }

                    Line _line = ObjectManager.LineManager.GetLine(Workbench.ServerName);
                    if (_line == null)
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Receive);
                        _s += Helper.GetDesc(Globals.ReturnCode.Common.RC_LINE_NOT_FOUND, new object[] { Workbench.ServerName }, true);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                        return;
                    }

                    if (_sGlassNumber.Trim() == "0000" || _sGlassNumber.Trim() == "FFFF")
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Receive);
                        _s += string.Format("Glass No<{0}> Format Error.", _sGlassNumber);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                        return;
                    }

                    Job job = ObjectManager.JobManager.GetJobByNo(_sGlassNumber);

                    if (job == null) 
                    {
                        //Glass Data not found
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Receive);
                        _s +=  string.Format("Can not find glass wip. GlassNo<{0}>, GlassID<{1}>", _sGlassNumber, _sGlassID);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);

                        //註解 : CF8A-C#Migration - EHC02GlassDataInquireGlassID
                        return;
                        //註解 : CF8A-C#Migration - EHC02GlassDataInquireGlassID 8A 不存在WIP 整段mark
                        # region
                        //[Start] v1.0.0.8-13
                        //job = ObjectManager.JobManager.CreateJob(_sGlassNumber);
                        //[End] v1.0.0.8-13                        


                        //job._GlassID._BCSGlassID = job._GlassID._EQPGlassID = _sGlassID; //v1.0.0.8-8
                        //_s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Receive);
                        //_s += string.Format("Create glass wip. GlassNo<{0}>, GlassID<{1}>", _sGlassNumber, _sGlassID);
                        //this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                        //IsWipUpData = false;
                        # endregion
                    }

                    Cassette _cst = job._SourceCassette.NullOrEmpty() ? null : job._SourceCassette; //v1.********                 

                    if(_cst == null)
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Receive);
                        _s += MISC.Helper.GetDesc(Globals.ReturnCode.Common.RC_CST_NOT_FOUND_BY_ID, new object[] { job._SourceCassette.NullOrEmpty() ? string.Empty : job._SourceCassette._CassetteID }, true);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    }

                    if (_eqp.File._EqpControlMode == eEQPControlMode.OFFLINE_MODE)
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                        _s += string.Format("EQP<{0}> Control Status isn't OnLine.", _inputData.Metadata.NodeNo);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    }

                    if (_eqp.File._Status.EQPStatus != eEQPStatus.STOP)
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                        _s += string.Format("EQP<{0}> Status isn't Stop.", _inputData.Metadata.NodeNo);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    }

                    lock (job)
                    {
                        //[Start]v1.0.0.36-4
                        job._JobData = _inputData.EventGroups[0].Events[0].ToJobData(ParameterManager["BCS_FLIP_BIN_VALUE"].GetBoolean());
                        //[End]v1.0.0.36-4
                        //20230329 EHC03GlassDataRegisterReport BUG FIX job._JobData._GlassID = job._GlassID._BCSGlassID;
                        job._JobData._GlassID = job._GlassID._BCSGlassID;
                        job._RawData = _inputData.EventGroups[0].Events[0].RawData.ShortArrayToString(); //v1.0.0.56-5//v1.0.0.156-2
                        _raw = _inputData.EventGroups[0].Events[0].RawData.ShortArrayToString();//v1.0.0.153-1//v1.0.0.156-2
                        if (job.RemoveFlag)
                        {
                            //--- 如果有了move out的時間,則表示可能被拿出去過
                            ObjectManager.HistoryManager.Record_SHIS_GlassLineHistory(_eqp, _line, _cst, job, DateTime.Now.ToString(), "", MethodBase.GetCurrentMethod().Name, _inputData.TrackKey); //v1.0.0.41-16
                        }
                        job.RemoveFlag = false;
                        job.RemoveReason = "";
                        job.RemoveOperatorID = "";
                        ObjectManager.JobManager.EnqueueSave(job);
                    }

                    //save wipglass DB
                    ObjectManager.HistoryManager.Record_SHIS_WIPGlass(_line, _eqp, _cst, job, IsWipUpData, _inputData.TrackKey);

                    //save GlassDatainquire DB
                    ObjectManager.HistoryManager.Record_SHIS_GlassDataInquire(_eqp, _line, job, MethodBase.GetCurrentMethod().Name, _inputData, _inputData.TrackKey, _raw);//v1.0.0.153-1
                }
            } catch (Exception ex) {
                LogError(MethodBase.GetCurrentMethod().Name + "()", ex);
            }
        }
        #endregion
        #region 4. Glass Data Modify (EHC04)
        public void EHC04GlassDataModify(Trx _inputData) {
            string _s = string.Empty;
            bool IsWipUpData = true;
            //20221008-CF8A-C#Migration
            eOnOff _triggerBit = eOnOff.OFF;
            Trx _outputData = null;
            try {
                WriteEvtTrxLog(_inputData, MethodBase.GetCurrentMethod().Name);
                if (_inputData.IsInitTrigger) return;
                //註解 : CF8A-C#Migration - mark
                //string _sGlassNumber = _inputData.EventGroups[0].Events[0].Items["GlassNumber"].Value.ToUpperTrim();
                //string _sGlassID = _inputData.EventGroups[0].Events[0].Items["GlassID"].Value.ToUpperTrim();
                //註解 : CF8A-C#Migration - modify
                RawData_EHC04GlassDataModify EHC04 = new RawData_EHC04GlassDataModify(_inputData);
                string _sGlassNumber = EHC04._raw_sGlassNumber;
                //註解 : CF8A-C#Migration - EHC04GlassDataModify
                //string _sGlassID = EHC04._raw_sGlassID;
                string _sGlassID = string.Empty;

                _outputData = GetPLCSyncReadTrx(_inputData.Metadata.NodeNo, Globals.TrxName.PLC.MTL.HHC04GlassDataModifyReply);  //20220224-CF8A-C#Migration - 新增HHC04GlassDataModifyReply
                if (_outputData == null)
                {
                    _s = string.Empty;
                    _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Send);
                    _s += "BCS NOT FOUND OUTPUTDATA OBJECT.";
                    this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    return;
                }

                _outputData.ClearTrxWith0();
                //end

                eOnOff triggerBit = (eOnOff)int.Parse(_inputData.EventGroups[0].Events[1].Items[0].Value);

                _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                //_s += string.Format("GlassID<{0}>, GlassNumber<{1}>, EHC04GlassDataModify<{2}>.", _sGlassID, _sGlassNumber, triggerBit);
                _s += string.Format("GlassNumber<{0}>, EHC04GlassDataModify<{1}>.", _sGlassNumber, triggerBit); //20220224-CF8A-C#Migration - EHC04GlassDataModify過濾GlassID
                this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);

                if (triggerBit == eOnOff.ON) {
                    Line _line = ObjectManager.LineManager.GetLine(Workbench.ServerName);
                    if (_line == null)
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Receive);
                        _s += Helper.GetDesc(Globals.ReturnCode.Common.RC_LINE_NOT_FOUND, new object[] { Workbench.ServerName }, true);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                        return;
                    }

                    Equipment _eqp = ObjectManager.EquipmentManager.GetEQP(_inputData.Metadata.NodeNo);
                    if (_eqp == null)
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Receive);
                        _s += Helper.GetDesc(Globals.ReturnCode.Common.RC_EQP_NOT_FOUND, new object[] { _inputData.Metadata.NodeNo }, true);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                        return;
                    }

                    if (_sGlassNumber.Trim() == "0000" || _sGlassNumber.Trim() == "FFFF")
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Receive);
                        _s += string.Format("Glass No<{0}> Format Error.", _sGlassNumber);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                        return;
                    }

                    Job job = ObjectManager.JobManager.GetJobByNo(_sGlassNumber);
                    if (job == null)
                    {
                        //Glass Data not found
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Receive);
                        _s += string.Format("Can not find glass wip. GlassNo<{0}>, GlassID<{1}>", _sGlassNumber, _sGlassID);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                        return;
                    }


                    Cassette _cst = job._SourceCassette.NullOrEmpty() ? null : job._SourceCassette; //v1.********
                    if (_cst == null)
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Receive);
                        _s += _s += MISC.Helper.GetDesc(Globals.ReturnCode.Common.RC_CST_NOT_FOUND_BY_ID, new object[] { job._SourceCassette.NullOrEmpty() ? string.Empty : job._SourceCassette._CassetteID }, true);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                        return;
                    }
                    
                    //更新GlassData資料
                    if (_eqp.File._EqpControlMode == eEQPControlMode.OFFLINE_MODE)
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                        _s += string.Format("EQP<{0}> Control Status isn't OnLine.", _inputData.Metadata.NodeNo);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    }

                    if (_eqp.File._Status.EQPStatus != eEQPStatus.STOP)
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                        _s += string.Format("EQP<{0}> Status isn't Stop.", _inputData.Metadata.NodeNo);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    }

                    //[Start]v1.0.0.36-4
                    _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                    _s += string.Format("Old Job Data: <{0}>, ", job._RawData);
                    Job _oldJob = job.Clone() as Job;
                    
                    _oldJob._RawData = job._RawData; //v1.0.0.56-4
                    //[End]v1.0.0.36-4

                    lock (job)
                    {
                        //[Start]v1.0.0.36-4
                        //Trx2Job(_inputData.Metadata.NodeNo, _inputData); 
                        job._JobData = _inputData.EventGroups[0].Events[0].ToJobData(ParameterManager["BCS_FLIP_BIN_VALUE"].GetBoolean());
                        //20230329 EHC04GlassDataModify BugFix _job._JobData._GlassID = _job._GlassID._BCSGlassID
                        job._JobData._GlassID = job._GlassID._BCSGlassID;
                        job._RawData = _inputData.EventGroups[0].Events[0].RawData.ShortArrayToString();//v1.0.0.155-1
                        //[End]v1.0.0.36-4

                        if (job._JobData._GroupNumber < 0 || job._JobData._GroupNumber > 15)
                        {
                            job._JobData._GroupNumber = 0;
                        }

                        job.RemoveFlag = false;
                        job.RemoveReason = "";
                        job.RemoveOperatorID = "";
                        ObjectManager.JobManager.EnqueueSave(job);
                    }

                    //[Start]v1.0.0.36-4
                    Job _newJob = job.Clone() as Job;
                    _newJob._RawData = _inputData.EventGroups[0].Events[0].RawData.ShortArrayToString(); //v1.0.0.59-5

                    _s += string.Format("Updata Job Data: <{0}>.", job._RawData);
                    this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    //[End]v1.0.0.36-4

                    //save 
                    ObjectManager.HistoryManager.Record_SHIS_GlassLineHistory(_eqp, _line, _cst, job, DateTime.Now.ToString(), "", MethodBase.GetCurrentMethod().Name, _inputData.TrackKey); //v1.0.0.41-16

                    //save wipglass DB
                    ObjectManager.HistoryManager.Record_SHIS_WIPGlass(_line, _eqp, _cst, job, IsWipUpData, _inputData.TrackKey);
                    //20230218
                    _outputData.EventGroups[0].Events[0].Items[0].Value = "1";//OK

                    //SHIS_GLASSDATAMODIFY TODO
                    ObjectManager.HistoryManager.Record_SHIS_GlassDataModify(_eqp, _oldJob, _newJob, MethodBase.GetCurrentMethod().Name, _inputData);
                }
            } catch (Exception ex) {
                LogError(MethodBase.GetCurrentMethod().Name + "()", ex);
            }
            //20221008-CF8A-C#Migration
            finally
            {
                //[Start] v1.0.0.24-7
                if (!_outputData.NullOrEmpty())
                {
                    if (_triggerBit == eOnOff.ON)
                    {
                        Trx _trx = GetPLCSyncReadTrx(_inputData.Metadata.NodeNo, Globals.TrxName.PLC.LTM.EHC04GlassDataModify);
                        if (!_trx.NullOrEmpty())
                        {
                            if ((eOnOff)_trx.EventGroups[0].Events[1].Items[0].Value.Parse() == eOnOff.OFF)
                            {
                                _outputData.ClearTrxWith0();
                                //_isReqBitClear = true;

                                _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                                _s += string.Format("Request Bit Has Clear.");
                                //this.Logger.LogErrorWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);//v1.0.0.183-11
                                this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);//v1.0.0.183-11
                            }
                        }
                        else
                        {
                            _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                            _s += Helper.GetDesc(Globals.ReturnCode.Common.RC_TRX_NOT_FOUND, new object[] { string.Format("{0}_{1}", _inputData.Metadata.NodeNo, Globals.TrxName.PLC.LTM.EHC04GlassDataModify) }, true);
                            this.Logger.LogErrorWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", string.Format("{0}", _s));

                            _outputData.ClearTrxWith0();
                        }
                    }

                    //[Start]v1.0.0.107-1
                    if (ObjectManager.HSInfoManager.IsHSValid(_inputData, _inputData.EventGroups[0].Events[0], 0, 0, _triggerBit))
                    {
                        SendPLCData(_outputData, _inputData.TrackKey);
                    }

                    if (_triggerBit == eOnOff.ON)
                    {
                        if (_timerManager.IsAliveTimer(string.Format("{0}", _outputData.Name.Trim()))) _timerManager.TerminateTimer(string.Format("{0}", _outputData.Name.Trim()));
                        _timerManager.CreateTimer(string.Format("{0}", _outputData.Name.Trim()),
                            false,
                            //(ParameterManager["S_BC_ACTIVE_TRIGGER_TIME_OUT"].GetInteger() * 1000),
                            ParameterManager["T1"].GetInteger(), //6000 (6s)
                            new System.Timers.ElapsedEventHandler(EHC04GlassDataModify_OFF),
                            new object[] { _outputData.Metadata.NodeNo, _outputData.Name.Trim(), _inputData.TrackKey });
                    }
                    //[End]v1.0.0.107-1

                }
                //[End] v1.0.0.24-7
            }
            //[End]v1.0.0.18-2

        }
        //20221008-CF8A-C#Migration
        //[Start]v1.0.0.107-1
        void EHC04GlassDataModify_OFF(object subjet, System.Timers.ElapsedEventArgs e)
        {
            string _s = string.Empty;
            string _ss = string.Empty;
            try
            {
                UserTimer timer = subjet as UserTimer;
                object[] _objs = (object[])timer.State;

                string _nodeNo = (string)_objs[0];
                string _trxName = (string)_objs[1];
                string _trcaeKey = (string)_objs[2];

                _ss = Helper.GetLogTitle(_nodeNo, _trcaeKey);

                Trx _trx = GetPLCSyncReadTrx(_nodeNo, _trxName.Replace(string.Format("{0}_", _nodeNo), ""));
                if (_trx.NullOrEmpty())
                {
                    _s = Helper.GetDesc(Globals.ReturnCode.Common.RC_TRX_NOT_FOUND, new object[] { string.Format("{0}_{1}", _nodeNo, _trxName.Replace(string.Format("{0}_", _nodeNo), "")) }, true);
                    this.Logger.LogErrorWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", string.Format("{0}{1}", _ss, _s));
                    return;
                }

                if (_trx.EventGroups[0].Events[0].Items[0].Value != "0")
                {
                    _trx.ClearTrxWith0();
                    SendPLCData(_trx, _trcaeKey);
                    _s = string.Format("TrxName<{0}>, TraceKey<{1}>: The reply Data Is Exist, So it will clear.", _trxName, _trcaeKey);
                    this.Logger.LogErrorWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", string.Format("{0}{1}", _ss, _s));
                }
            }
            catch (Exception ex)
            {
                LogError(MethodBase.GetCurrentMethod().Name + "()", ex);
            }
        }
        //[End]v1.0.0.107-1

        #endregion
        #region 5. NG Mark Reason Code Report (EHC11)
        public void NGMarkAutoSettingModeSwitchBlock(Trx _inputData) {
            string _s = string.Empty;
            try {
                WriteEvtTrxLog(_inputData, MethodBase.GetCurrentMethod().Name);                

                #region Get Equipment Information...
                Equipment _eqp = ObjectManager.EquipmentManager.GetEQP(_inputData.Metadata.NodeNo);
                if (_eqp == null) {
                    _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                    _s += Helper.GetDesc(Globals.ReturnCode.Common.RC_EQP_NOT_FOUND, new object[] { _inputData.Metadata.NodeNo }, true); //BCS NOT FOUND EQUIPMENT OBJECT.";
                    this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    SendOPITerminalMessage(_inputData.TrackKey, eTerminalMessageType.Alarm, Helper.GetDesc("SEVEREINFO"), _s);
                    return;
                }
                #endregion

                lock (_eqp) {

                    _eqp.File._Switch._NgMarkAutoSettingMode = (_inputData.EventGroups[0].Events[0].Items["NGMarkAutoSettingModeSwitchBlock"].Value == "0" ? eEnabledDisabled.DISABLED : eEnabledDisabled.ENABLED);
                    ObjectManager.EquipmentManager.EnqueueSave(_eqp.File);

                    _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                    _s += string.Format("NgMarkAutoSettingMode<{0}>.", _eqp.File._Switch._NgMarkAutoSettingMode);
                    this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                }

                _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                _s += MISC.Helper.GetDesc(Globals.Resources.NGMarkAutoSettingModeSwitch, new object[] { 
                    (_eqp.File._Switch._NgMarkAutoSettingMode == eEnabledDisabled.ENABLED ? MISC.Helper.GetDesc("ENABLED") : MISC.Helper.GetDesc("DISABLED")) });
                this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                SendOPITerminalMessage(eTerminalMessageType.Info, MISC.Helper.GetDesc("INFO"), _s);

            } catch (Exception ex) {
                LogError(MethodBase.GetCurrentMethod().Name + "()", ex);
            }
        }
        public void EHC11NGMarkReasonCodeReport(Trx _inputData) {
            string _s = string.Empty;
            try {
                WriteEvtTrxLog(_inputData, MethodBase.GetCurrentMethod().Name);               

                eOnOff _bit = (eOnOff)int.Parse(_inputData.EventGroups[0].Events[1].Items[0].Value);
                if (_bit == eOnOff.OFF) return;

                // 註解 : CF8A-C#Migration - 修改CF8A拆解資料方法.- Merge後 - (0210)
                RawData_EHC11NGMarkReasonCodeReport EHC11 = new RawData_EHC11NGMarkReasonCodeReport(_inputData);

                //string _sGlassNumber = _inputData.EventGroups[0].Events[0].Items["GlassNumber"].Value.ToUpperTrim();
                //string _ngMarkCode = _inputData.EventGroups[0].Events[0].Items["NGMarkcode"].Value;
                //string _glassNGmarkReasonCode = _inputData.EventGroups[0].Events[0].Items["GlassNGmarkReasonCode"].Value;
                //true=Set, false=Reset
                //bool _setFlag = (int.Parse(_inputData.EventGroups[0].Events[0].Items["Action"].Value) == 0 ? true : false);

                string _sGlassNumber = EHC11._raw_W_glassnumber;
                string _ngMarkCode = EHC11._raw_W_ngmarkcode;
                string _glassNGmarkReasonCode = EHC11._raw_W_glassngmarkreasoncode;
                bool _setFlag = (EHC11._raw_W_action == 0 ? true : false); 
                //end

                _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                _s += string.Format("GlassNumber<{0}>, NGMarkcode<{1}>, GlassNGmarkReasonCode<{2}>, Action<{3}>.", _sGlassNumber, _ngMarkCode, _glassNGmarkReasonCode, _setFlag);
                this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);

                Job _job = ObjectManager.JobManager.GetJobByNo(_sGlassNumber);

                if (_job == null) {
                    _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                    _s += MISC.Helper.GetDesc(Globals.ReturnCode.Common.RC_JOB_NOT_FOUND, new object[] { _sGlassNumber }, true);
                    this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    SendOPITerminalMessage(_inputData.TrackKey, eTerminalMessageType.Alarm, Helper.GetDesc("SEVEREINFO"), _s);
                    return;
                }

                Equipment _eqp = ObjectManager.EquipmentManager.GetEQP(_inputData.Metadata.NodeNo);
                if (_eqp == null) {
                    _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                    _s += Helper.GetDesc(Globals.ReturnCode.Common.RC_EQP_NOT_FOUND, new object[] { _inputData.Metadata.NodeNo }, true); //BCS NOT FOUND EQUIPMENT OBJECT.";
                    this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    SendOPITerminalMessage(_inputData.TrackKey, eTerminalMessageType.Alarm, Helper.GetDesc("SEVEREINFO"), _s);
                    return;
                }

                if (_ngMarkCode.Trim() == string.Empty) {
                    _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                    _s += string.Format("NGMark Code mismatch! This Line does not define the NGMarkCode:{0}", _ngMarkCode);
                    this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    return;
                }

                lock (_job) {
                    if (_setFlag) {
                        _job._JobInfo._NGMarkPLC = _ngMarkCode.Parse(); //V1.0.0.30-4
                        _job._JobInfo._NGMarkReasonCode = _glassNGmarkReasonCode; //V1.0.0.30-4
                        _job._JobInfo._NGMark = Helper.NGMarkCode(_ngMarkCode.Parse());//v1.0.0.151-12
                        _job._JobData._NGMarkCode = _ngMarkCode.Parse();//v1.0.0.152-5
                        cNGMark _ngMark = null;

                        string _key = string.Format(Globals.Keys.NGMarkKey, _inputData.Metadata.NodeNo, _ngMarkCode);
                        if (_job._NGMarkInfo != null && _job._NGMarkInfo.ContainsKey(_key)) _ngMark = _job._NGMarkInfo[_key] as cNGMark;

                        if (_ngMark == null) {
                            //Create NGMark!!
                            _ngMark = new cNGMark();
                            _ngMark._NodeNo = _inputData.Metadata.NodeNo;
                            _ngMark._NGMarkCode = _ngMarkCode;
                            _ngMark._GlassNGMarkReasonCode = _glassNGmarkReasonCode;
                        } else {
                            //Update NGMark!! by NodeNo!!
                            _ngMark._NGMarkCode = _ngMarkCode;
                            _ngMark._GlassNGMarkReasonCode = _glassNGmarkReasonCode;
                            _ngMark._RptDateTime = DateTime.Now;
                        }

                        #region Special rule for Aligner node of CNVR line...
                        switch (Workbench.Instance.LineType()) {
                            case Globals.LineType.CNVR_LINE_FLAG:
                                //93.0.0.14 20200331 bibby  防呆
                                if (!_job._CurrentNodeNo.NullOrEmpty())
                                {
                                    //>
                                    if (ParameterManager["S_CHECK_ILSP_BEFORE_ALIGNER_FLAG"].GetBoolean())
                                    {
                                        if ((int.Parse(_job._CurrentNodeNo.Replace('L', ' ')) > 8) && (int.Parse(_job._CurrentNodeNo.Replace('L', ' ')) != 13))
                                        { //v1.0.0.21-5

                                            //if ((ObjectManager.JobManager.NGMarkDesc(_sNGMarkcode, _job._LineType) == "B") ||
                                            //   (ObjectManager.JobManager.NGMarkDesc(_sNGMarkcode, _job._LineType) == "C") ||
                                            //   (ObjectManager.JobManager.NGMarkDesc(_sNGMarkcode, _job._LineType) == "H") ||
                                            //   (ObjectManager.JobManager.NGMarkDesc(_sNGMarkcode, _job._LineType) == "T") ||
                                            //   (ObjectManager.JobManager.NGMarkDesc(_sNGMarkcode, _job._LineType) == "A")
                                            //  ) {
                                            //    _job._NGMarkInfo[_job._JobKey]._AfterAlignerNGMark = true;

                                            //    _s = string.Empty;
                                            //    _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                                            //    _s += string.Format("EQ={0}_{1},Set NG Mark={2},After Aligner,Make for GlassNO<{3}> After Aligner NG Mark Flag=<ON>!", job.CurrentEQPNo, job._LineID, _sNGMarkcode, job._GlassNumberHex);
                                            //    this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                                            //}
                                        }
                                    }
                                    else
                                    {
                                        if (int.Parse(_job._CurrentNodeNo.Replace('L', ' ')) > 8)
                                        { //v1.0.0.21-5
                                            //if ((ObjectManager.JobManager.NGMarkDesc(_sNGMarkcode, _job._LineType) == "B") ||
                                            //   (ObjectManager.JobManager.NGMarkDesc(_sNGMarkcode, _job._LineType) == "C") ||
                                            //   (ObjectManager.JobManager.NGMarkDesc(_sNGMarkcode, _job._LineType) == "H") ||
                                            //   (ObjectManager.JobManager.NGMarkDesc(_sNGMarkcode, _job._LineType) == "T") ||
                                            //   (ObjectManager.JobManager.NGMarkDesc(_sNGMarkcode, _job._LineType) == "A")
                                            //  ) {
                                            //    _job._NGMarkInfo[_job._JobKey]._AfterAlignerNGMark = true;

                                            //    _s = string.Empty;
                                            //    _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                                            //    _s += string.Format("EQ={0}_{1},Set NG Mark={2},After Aligner,Make for GlassNO<{3}> After Aligner NG Mark Flag=<ON>!", job.CurrentEQPNo, job._LineID, _sNGMarkcode, job._GlassNumberHex);
                                            //    this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                                            //}
                                        }
                                    }
                                //<93.0.0.14 20200331 bibby  防呆
                                }
                                //>
                                break;
                        }
                        #endregion

                    } else {
                        //Reset 抹掉目前有記錄的NGMark, 不只可以抹掉自己的, 還可以抹掉其他機台打的NGMark記錄!! 已經被reset的ngmark不需要再處理!!
                        IList<cNGMark> _ngMarks = _job._NGMarkInfo.Values.Where(_nm => _nm._NGMarkCode.ToUpper().Trim() == _ngMarkCode.ToUpper().Trim() && _nm._ResetFlag == false).ToList();

                        if (_ngMarks == null || _ngMarks.Count <= 0) {
                            //just log! do nothing!
                            _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                            _s += MISC.Helper.GetDesc(Globals.ReturnCode.PLC.Job.NGMarksNotFound, new object[] { _ngMarkCode, _sGlassNumber, _glassNGmarkReasonCode }, true);
                            this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);

                        } else {
                            //將找到符合的記錄給抹掉, 需要記錄抹掉記錄的NodeNo!!
                            foreach (cNGMark _nm in _ngMarks) {
                                _nm._GlassNGMarkReasonCode = _glassNGmarkReasonCode;
                                _nm._ResetFlag = true;
                                _nm._ResetNodeNo = _inputData.Metadata.NodeNo;
                                _nm._RptDateTime = DateTime.Now;

                                _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                                _s += MISC.Helper.GetDesc(Globals.Resources.NGMarkReset, new object[] { _sGlassNumber, _ngMarkCode, _nm._ResetNodeNo, _nm._NodeNo, _nm._LastRptDateTime.ToString("yyyy-MM-dd HH:mm:ss") });
                                this.Logger.LogDebugWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                            }
                        }
                    }
                    ObjectManager.JobManager.EnqueueSave(_job);
                }

                _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                _s += string.Format("NGMark Reason Report - process completed, GlassNo=<{0}>", _job._GlassNumberHex);
                this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);

                //v1.0.0.1-6
                //SHIS_NGMARKREASONCODEREPORT Table, TODO
                ObjectManager.HistoryManager.Record_SHIS_NGMarkReasonCodeReport(_eqp, _job, _setFlag, ObjectManager.JobManager.GetNgMarkCodeDesc(_ngMarkCode), _glassNGmarkReasonCode); //v1.0.0.41-8

            } catch (Exception ex) {
                LogError(MethodBase.GetCurrentMethod().Name + "()", ex);
            }
        }
        #endregion
        #region 6. Glass Data Erase (EHC05)
        public void EHC05GlassDataErase(Trx _inputData) {
            string _s = string.Empty;
            try {
                WriteEvtTrxLog(_inputData, MethodBase.GetCurrentMethod().Name);
                if (_inputData.IsInitTrigger) return;
                //註解 : CF8A-C#Migration - mark
                //string _sGlassNumber = _inputData.EventGroups[0].Events[0].Items["GlassNumber"].Value.ToUpperTrim();
                //string _sOperatorID = _inputData.EventGroups[0].Events[0].Items["GlassEraseOperatorID"].Value.ToString();
                //string _sReasonCode = _inputData.EventGroups[0].Events[0].Items["GlassEraseReasonCode"].Value.ToString();
                //註解 : CF8A-C#Migration - modify
                RawData_EHC05GlassDataErase EHC05 = new RawData_EHC05GlassDataErase(_inputData);
                string _sGlassNumber = EHC05._raw_glassno;
                string _sOperatorID = EHC05._raw_sOperatorID;
                string _sReasonCode = EHC05._raw_sReasonCode;

                Tuple<string, string> _tmp = Tuple.Create(_sOperatorID, _sReasonCode); // 註解 : CF8A-C#Migration.

                eOnOff triggerBit = (eOnOff)int.Parse(_inputData.EventGroups[0].Events[1].Items[0].Value);

                _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                _s += string.Format("GlassNumber<{0}>, GlassEraseOperatorID<{1}>, GlassEraseReasonCode<{2}>, EHC05GlassDataEraseRequest_Bit<{3}>.", _sGlassNumber, _sOperatorID, _sReasonCode, triggerBit);
                this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);

                if (triggerBit == eOnOff.ON) {
                    Line _line = ObjectManager.LineManager.GetLine(Workbench.ServerName);
                    if (_line == null)
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Receive);
                        _s += Helper.GetDesc(Globals.ReturnCode.Common.RC_LINE_NOT_FOUND, new object[] { Workbench.ServerName }, true);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                        return;
                    }

                    Equipment _eqp = ObjectManager.EquipmentManager.GetEQP(_inputData.Metadata.NodeNo);
                    if (_eqp == null)
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Receive);
                        _s += Helper.GetDesc(Globals.ReturnCode.Common.RC_EQP_NOT_FOUND, new object[] { _inputData.Metadata.NodeNo }, true);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                        return;
                    }

                    Job job = ObjectManager.JobManager.GetJobByNo(_sGlassNumber);
                    if (job == null) {
                        //找不到glass記log
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Receive);
                        _s += _s += MISC.Helper.GetDesc(Globals.ReturnCode.Common.RC_JOB_NOT_FOUND, new object[] { _sGlassNumber }, true);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                        SendOPITerminalMessage("GlassDataEras HandShaking Error", string.Format("Can not find glass wip. GlassNo[{0}]", _sGlassNumber));
                        return;
                    }

                    Cassette _cst = job._SourceCassette.NullOrEmpty() ? null : job._SourceCassette;//v1.0.0.9-13
                    if (_cst == null)
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Receive);
                        _s += _s += MISC.Helper.GetDesc(Globals.ReturnCode.Common.RC_CST_NOT_FOUND_BY_ID, new object[] { job._SourceCassette.NullOrEmpty() ? string.Empty : job._SourceCassette._CassetteID }, true);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                        return;
                    }

                    if(job._LastNodeNo != _inputData.Metadata.NodeNo)
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Receive);
                        _s += _s += MISC.Helper.GetDesc(Globals.ReturnCode.PLC.RC_WIP_NODENO_INFO_NOT_MATCH, new object[] { _sGlassNumber, job._JobData._GlassID, job._LastNodeNo, _inputData.Metadata.NodeNo }, true);
                    }

                    if (_eqp.File._EqpControlMode == eEQPControlMode.OFFLINE_MODE)
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                        _s += string.Format("EQP<{0}> Control Status isn't OnLine.", _inputData.Metadata.NodeNo);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    }

                    if (_eqp.File._Status.EQPStatus != eEQPStatus.STOP)
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                        _s += string.Format("EQP<{0}> Status isn't Stop.", _inputData.Metadata.NodeNo);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    }

                    lock (job)
                    {
                        job._JobInfo._ReasonCode = _sReasonCode;
                        job.RemoveFlag = true;
                        job.RemoveReason = _sReasonCode;
                        job.RemoveOperatorID = _sOperatorID;
                        ObjectManager.JobManager.EnqueueSave(job);
                    }

                    DateTime moveOutTime = DateTime.Now;

                    //Report BPCSSCRP, TODO
                    if (_line.File.HostMode != eHostMode.OFFLINE)
                    {
                        Invoke(Globals.ServiceName.MESService, "GlassDataEraseReport", new object[] { _eqp, _line, _cst, job, _sOperatorID, _sReasonCode, _inputData.TrackKey });
                    }

                    //Save GlassLineHistory DB
                    ObjectManager.HistoryManager.Record_SHIS_GlassLineHistory(_eqp, _line, _cst, job, job.CreateTime.ToString(), moveOutTime.ToString(), MethodBase.GetCurrentMethod().Name, _inputData.TrackKey); //v1.0.0.41-16

                    //Add By Kevin.Chen 2010/3/29 當CST Seq =255代表Daily/Limit Glass同步觸發Buffer Data Exchange HS (maurice: Glass 破在外面, BC 通知buffer刪除)
                    if (int.Parse(job._JobData._CassetteSequenceNo) == 255)
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                        _s += "Daily/Limit Glass Auto Start Buffer Data Exchange Handshake!";
                        this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);

                        //call BufferDataExchange, TODO
                        HHS07BufferSlotDataExchangeReq(_inputData.Metadata.NodeNo, job, _sGlassNumber, eOnOff.ON, _inputData.TrackKey);
                    }

                    //Save SHIS_GLASSDATAERASE TABLE
                    // 註解 : CF8A-C#Migration.
                    //ObjectManager.HistoryManager.Record_SHIS_GlassDataErase(_eqp, _line, _cst, job, MethodBase.GetCurrentMethod().Name, _inputData);
                    ObjectManager.HistoryManager.Record_SHIS_GlassDataErase(_eqp, _line, _cst, job, MethodBase.GetCurrentMethod().Name, _inputData, _tmp);
                    //end

                    //Updata WIPGLASS DB
                    ObjectManager.HistoryManager.Record_SHIS_WIPGlass(_line, _eqp, _cst, job, true, _inputData.TrackKey);

                    
                }
            } catch (Exception ex) {
                LogError(MethodBase.GetCurrentMethod().Name + "()", ex);
            }
        }
        #endregion
        #region 7. Glass and Glass Data Match Function (EQP only)
        #endregion
        //20250701
        #region 8. Glass Out For Bridge (EHC35) / Glass Out For Bridge
        public void EHC35GlassOutForBridge(Trx _inputData)
        {
            string _s = string.Empty;
            Job _job = null;
            Equipment _eqp = null;
            bool _isReqBitClear = false;
            eOnOff _triggerBit = eOnOff.OFF;
            //20250807
            Trx _outputData = GetPLCSyncReadTrx(_inputData.Metadata.NodeNo, Globals.TrxName.PLC.MTL.HHS35GlassOutForBridgeReply);
            //Trx _outputData = GetPLCSyncReadTrx(_inputData.Metadata.NodeNo, Globals.TrxName.PLC.MTL.HHC06GlassIDInquireReply);
            if (_outputData == null)
            {
                _s = string.Empty;
                _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Send);
                _s += "BCS NOT FOUND OUTPUTDATA OBJECT.";
                this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                return;
            }

            _outputData.ClearTrxWith0();

            try
            {
                WriteEvtTrxLog(_inputData, MethodBase.GetCurrentMethod().Name);
                if (_inputData.IsInitTrigger) return;

                //註解 : CF8A-C#Migration - modify                        
                RawData_EHC35GlassOutForBridge EHC35 = new RawData_EHC35GlassOutForBridge(_inputData);


                _triggerBit = (eOnOff)int.Parse(_inputData.EventGroups[0].Events[1].Items[0].Value);
                _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                //註解 : CF8A-C#Migration - mark
                //_s += string.Format("GlassNumber<{0}>, EHC06GlassIDInquireRequest_Bit<{1}>.", _inputData.EventGroups[0].Events[0].Items["GlassNumber"].Value.ToUpperTrim(), _triggerBit);
                //註解 : CF8A-C#Migration - modify
                _s += string.Format("GlassNumber<{0}>, EHC35GlassOutForBridge_Bit<{1}>.", EHC35._raw_glassno, _triggerBit);

                this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);

                int itemCount = 0;
                if (_inputData.EventGroups != null &&
                    _inputData.EventGroups.Count > 0 &&
                    _inputData.EventGroups[0].Events != null &&
                    _inputData.EventGroups[0].Events.Count > 0 &&
                    _inputData.EventGroups[0].Events[0] != null &&
                    _inputData.EventGroups[0].Events[0].Items != null)
                {
                    itemCount = _inputData.EventGroups[0].Events[0].Items.Count;
                }
                _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                _s += string.Format("EHC35 Events[0].Items Count: {0}", itemCount);
                this.Logger.LogDebugWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);

                //[Start]v1.0.0.107-1
                if (_triggerBit == eOnOff.ON)
                {
                    ObjectManager.HSInfoManager.AddHSInfoEntity(new HSInfoEntity(_inputData.Metadata.NodeNo,
                                                                                  _inputData.Name,
                                                                                  _inputData.TrackKey,
                                                                                  _inputData.EventGroups[0].Events[0],
                                                                                  0,
                                                                                  0));
                }
                else
                {
                    ObjectManager.HSInfoManager.DeleteHSInfoEntity(_inputData.Name);
                }
                //[End]v1.0.0.107-1

                if (_triggerBit == eOnOff.ON)
                {
                    #region Signal ON logic...
                    _eqp = ObjectManager.EquipmentManager.GetEQP(_inputData.Metadata.NodeNo);
                    if (_eqp == null)
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                        _s += "BCS NOT FOUND EQUIPMENT OBJECT.";
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    }
                    if (_eqp.File._EqpControlMode != eEQPControlMode.INLINE_REMOTE_MODE)
                    {
                        _s = string.Format("Equipment [{0}] is not in INLINE_REMOTE_MODE, current mode is [{1}]",
                            _eqp.Data.NODENO, _eqp.File._EqpControlMode);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    }
                    //註解 : CF8A-C#Migration - mark
                    //_job = ObjectManager.JobManager.GetJobByNo(_inputData.EventGroups[0].Events[0].Items["GlassNumber"].Value.ToUpperTrim());
                    //註解 : CF8A-C#Migration - modigy
                    _job = ObjectManager.JobManager.GetJobByNo(EHC35._raw_glassno);
                    if (_job == null)
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                        //註解 : CF8A-C#Migration - mark
                        //_s += string.Format("GlassNo<{0}>, No such Glass in WIP.", _inputData.EventGroups[0].Events[0].Items["GlassNumber"].Value.ToUpperTrim());
                        //註解 : CF8A-C#Migration - modigy
                        _s += string.Format("GlassNo<{0}>, No such Glass in WIP.", EHC35._raw_glassno);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);

                        _outputData.EventGroups[0].Events[0].Items[1].Value = "2"; //NG, Null

                        //註解 : CF8A-C#Migration - EHC06GlassIDInquire
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                        _s += string.Format("GlassID<{0}>, ReturnCode<{1}>.", _outputData.EventGroups[0].Events[0].Items[0].Value, _outputData.EventGroups[0].Events[0].Items[1].Value);
                        this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);

                    }
                    //20250701 待修正
                    // Report IAM/IMM
                    Line _line = ObjectManager.LineManager.GetLine();
                    if (Workbench.Instance.LineType() == Globals.LineType.CNVR_LINE_FLAG)
                    {
                        Equipment objMUINNode = null;
                        Equipment objAOINode = null;

                        // 尋找特定節點
                        foreach (Equipment objTmpNode in ObjectManager.EquipmentManager.GetEQPs())
                        {
                            if (objTmpNode.Data.NODENAME.ToUpperTrim() == "MUIN")
                            {
                                objMUINNode = objTmpNode;
                            }
                            else if (objTmpNode.Data.NODENAME.ToUpperTrim() == "AOI")
                            {
                                objAOINode = objTmpNode;
                            }
                        }

                        if (objMUINNode != null)
                        {
                            //MU_Handle_BatchGlass(strGlassNo, objMUINNode, false, _inputData.TrackKey);
                        }

                        if (objAOINode != null)
                        {
                            //MU_Handle_BatchGlass(strGlassNo, objAOINode, false, _inputData.TrackKey);
                        }
                    }

                    if (MU_Proc_GlassOut(_job, _eqp, _inputData) == true)
                    {
                    }

                    //20250807
                    HHS35GlassOutForBridgeReply(_inputData, eOnOff.ON);

                    // 特殊處理 - Bridge相
                    if ((Workbench.Instance.LineType() == Globals.LineType.INSP_LINE_FLAG || Workbench.Instance.LineType() == Globals.LineType.DMAC_LINE_FLAG) && _eqp.Data.NODENAME == "CV3")
                    {
                        if (_line.File._BridgeGlassNo == _job._GlassNumber)
                        {
                            _line.File._BridgeCheck = "Y";
                            ObjectManager.LineManager.EnqueueSave(_line.File);
                        }
                    }

                    #endregion
                    //註解 : CF8A-C#Migration - EHC06GlassIDInquire
                    //20250807
                    //if (_outputData.EventGroups[0].Events[0].Items[1].Value == "1")
                    //{
                    //    _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                    //    _s += string.Format("GlassID<{0}>, ReturnCode<{1}>.", _outputData.EventGroups[0].Events[0].Items[0].Value, _outputData.EventGroups[0].Events[0].Items[1].Value);
                    //    this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    //}
                    //else
                    //{
                    //    _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                    //    _s += string.Format("ReturnCode<{0}>.", _outputData.EventGroups[0].Events[0].Items[1].Value);
                    //    this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    //}
                    //註解 : CF8A-C#Migration - mark 
                    //_s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                    //_s += string.Format("GlassID<{0}>, ReturnCode<{1}>.", _outputData.EventGroups[0].Events[0].Items[0].Value, _outputData.EventGroups[0].Events[0].Items[1].Value);
                    //this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                }
            }
            catch (Exception ex)
            {
                LogError(MethodBase.GetCurrentMethod().Name + "()", ex);
            }
            finally
            {
                //[Start] v1.0.0.24-7
                if (!_outputData.NullOrEmpty())
                {
                    if (_triggerBit == eOnOff.ON)
                    {
                        string[] _temp = _inputData.Name.Split('_').ToArray();
                        if (!_temp.NullOrEmpty() && _temp.Count<string>() >= 2)
                        {
                            string _name = _temp[1];
                            Trx _trx = GetPLCSyncReadTrx(_inputData.Metadata.NodeNo, _name);
                            if (!_trx.NullOrEmpty())
                            {
                                if ((eOnOff)_trx.EventGroups[0].Events[1].Items[0].Value.Parse() == eOnOff.OFF)
                                {
                                    _outputData.ClearTrxWith0();
                                    _isReqBitClear = true;

                                    _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                                    _s += string.Format("Request Bit Has Clear.");
                                    //this.Logger.LogErrorWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);//v1.0.0.183-11
                                    this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);//v1.0.0.183-11
                                }
                            }
                            else
                            {
                                _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                                _s += Helper.GetDesc(Globals.ReturnCode.Common.RC_TRX_NOT_FOUND, new object[] { string.Format("{0}_{1}", _inputData.Metadata.NodeNo, _name) }, true);
                                this.Logger.LogErrorWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", string.Format("{0}", _s));

                                _outputData.ClearTrxWith0();
                            }
                        }
                        else
                        {
                            _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                            _s += Helper.GetDesc(Globals.ReturnCode.Common.RC_TRX_NOT_FOUND, new object[] { string.Format("{0}_{1}", _inputData.Metadata.NodeNo, string.Empty) }, true);
                            this.Logger.LogErrorWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", string.Format("{0}", _s));

                            _outputData.ClearTrxWith0();

                        }
                    }

                    //[Start]v1.0.0.107-1
                    if (ObjectManager.HSInfoManager.IsHSValid(_inputData, _inputData.EventGroups[0].Events[0], 0, 0, _triggerBit))
                    {
                        SendPLCData(_outputData, _inputData.TrackKey);
                    }

                    if (_triggerBit == eOnOff.ON)
                    {
                        if (_timerManager.IsAliveTimer(string.Format("{0}", _outputData.Name.Trim()))) _timerManager.TerminateTimer(string.Format("{0}", _outputData.Name.Trim()));
                        _timerManager.CreateTimer(string.Format("{0}", _outputData.Name.Trim()),
                            false,
                            //(ParameterManager["S_BC_ACTIVE_TRIGGER_TIME_OUT"].GetInteger() * 1000),
                            ParameterManager["T1"].GetInteger(), //6000 (6s)
                            new System.Timers.ElapsedEventHandler(EHC35GlassOutForBridge_OFF),
                            new object[] { _outputData.Metadata.NodeNo, _outputData.Name.Trim(), _inputData.TrackKey });
                    }
                    //[End]v1.0.0.107-1

                    if ((_triggerBit == eOnOff.ON) && (!_isReqBitClear))
                    {
                        //TODO:: Add History Information
                        //註解 : CF8A-C#Migration - mark
                        //ObjectManager.HistoryManager.Record_SHIS_GlassIDInquire(_eqp, _job, _inputData.EventGroups[0].Events[0].Items["GlassNumber"].Value.ToUpperTrim(), _outputData.EventGroups[0].Events[0].Items[0].Value, _outputData.EventGroups[0].Events[0].Items[1].Value); //v1.0.0.8-7//v1.0.0.183-14
                        //註解 : CF8A-C#Migration - modify
                        RawData_EHC35GlassOutForBridge EHC35 = new RawData_EHC35GlassOutForBridge(_inputData);
                        //20250701 待修正
                        //ObjectManager.HistoryManager.Record_SHIS_GlassIDInquire(_eqp, _job, EHC35._raw_glassno, _outputData.EventGroups[0].Events[0].Items[0].Value, _outputData.EventGroups[0].Events[0].Items[1].Value);
                        //

                    }
                }
            }
            //[End] v1.0.0.24-7
        }

        private bool MU_Proc_GlassOut(Job _job, Equipment _eqp, Trx _inputData)
        {
            string _s = string.Empty;
            cBridgeCST objBridgeCST = null;
            string[] szStr = null;
            List<string> glassNoHexList = new List<string>();
            List<Job> _lstJob = new List<Job>();
            int nCount = 0;
            // 添加玻璃移出歷史記錄
            string rawData = _job._RawData ?? string.Empty;
            string trackKey = _inputData.TrackKey ?? string.Empty;
            // 提供更有意義的參數值，而不是空字符串
            string operationCode = "BRIDGE"; // 或其他適合的操作代碼
            string remarks = "Glass Bridge Out INSP"; // 或其他適合的備註
            string additionalInfo = "Job ID: {_job.JobID}";

            try
            {
                // 獲取線體對象
                Line _line = ObjectManager.LineManager.GetLine(Core.Workbench.ServerName);

                // 根據玻璃的卡匣序號獲取Bridge卡匣對象
                objBridgeCST = ObjectManager.BridgeCSTManager.GetBridgeCSTBySeq(_job._JobData._CassetteSequenceNo);

                if (objBridgeCST == null)
                {
                    _s = string.Format("can not find Bridge CST,CSTSeq=<{0}>,FromLine=<{1}>",
                        _job._JobData._CassetteSequenceNo, _job._JobData._LineNumber);
                    this.Logger.LogErrorWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    return false;
                }

                // 更新Bridge卡匣的玻璃組
                objBridgeCST.GlassNoGroup = objBridgeCST.GlassNoGroup + "," + _job._GlassNumberHex;

                // 保存Bridge卡匣信息
                //20250703 待確認
                // 修改為:

                ObjectManager.BridgeCSTManager.SaveBridgeCST(objBridgeCST);


                // 記錄日誌
                _s = string.Format("EQP Report Glass No=<{0}>,Group of Glass No=<{1}>",
                    _job._GlassNumberHex, objBridgeCST.GlassNoGroup);
                this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                string _FuncNameIn = string.Empty;


                //20250703 待確認 添加玻璃移出歷史記錄
                ObjectManager.HistoryManager.Record_SHIS_GlassEQPHistory(
                _line, _eqp, _job,
                operationCode, rawData,
                eGlassMoveInOutReason.GLASS_STORED, eGlassMoveInOutReason.GLASS_STORED,
                additionalInfo, MethodBase.GetCurrentMethod().Name,
                DateTime.Now, _inputData.TrackKey);

                // 分割玻璃組字符串
                string[] glassNumbers = objBridgeCST.GlassNoGroup.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

                // 是否為第一片玻璃的標誌
                bool isFirstGlass = (objBridgeCST.FirstGlsNo == _job._GlassNumberHex);
                if (isFirstGlass)
                {
                    _s = string.Format("EQP Report Glass No=<{0}> is first Move Glass", _job._GlassNumberHex);
                    this.Logger.LogDebugWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                }

                // 處理所有玻璃
                foreach (string glassNo in glassNumbers)
                {
                    if (string.IsNullOrEmpty(glassNo))
                        continue;

                    glassNoHexList.Add(glassNo);

                    // 獲取當前玻璃對應的 Job
                    Job currentJob = ObjectManager.JobManager.GetJobByNo(glassNo);
                    if (currentJob != null)
                    {
                        _lstJob.Add(currentJob);

                        // 檢查並報告 Pass History
                        if (!currentJob.PassHisReportFlag &&
                            ParameterManager.ContainsKey("BCS_REPORTPASSHIS_BYGLASS") &&
                            ParameterManager.Parameters["BCS_REPORTPASSHIS_BYGLASS"].GetBoolean())
                        {
                            // 報告 Pass history
                            Invoke(Globals.ServiceName.MESService, Globals.TrxName.MES.GlassHistoryReportByGlass,
                                   new Object[] { _eqp, currentJob, currentJob._FromSlotNo, "" });

                            Invoke(Globals.ServiceName.MESService, Globals.TrxName.MES.TrackOutBridgeByGlass, new Object[] { _line, _eqp, _job, _inputData.TrackKey });
                            // 記錄 pass history report flag
                            lock (currentJob)
                            {
                                currentJob.PassHisReportFlag = true;
                                ObjectManager.JobManager.EnqueueSave(currentJob);
                            }
                        }
                    }

                    // 計數增加（僅在非第一片玻璃的情況下）
                    if (!isFirstGlass)
                        nCount++;

                    // 如果是第一片玻璃或達到報告計數，則報告並移除
                    //20250807
                    //bool shouldReportAndRemove = isFirstGlass || (!isFirstGlass && nCount == ParameterManager["S_BRIDGE_REPORT_COUNT"].GetInteger());
                    bool shouldReportAndRemove = isFirstGlass || 
                                    (!isFirstGlass && nCount == 1);

                    if (shouldReportAndRemove)
                    {
                        if (!isFirstGlass)
                        {
                            _s = string.Format("Group of Glass No=<{0}>,already reach report count,Count=<{1}>",
                                objBridgeCST.GlassNoGroup, 1);
                            this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                        }

                        // 從WIP中移除Bridge卡匣
                        if (_lstJob != null && _lstJob.Count > 0)
                        {
                            ObjectManager.JobManager.DeleteJobs(_lstJob, true);
                        }

                        string strKeyBridge = ObjectManager.BridgeCSTManager.CU_GetBridgeCSTKey(objBridgeCST.CSTSeq);
                        ObjectManager.BridgeCSTManager.DeleteBridgeCST(strKeyBridge, true);

                        // 已處理完畢，跳出循環
                        break;
                    }
                }


                return true;
            }
            catch (Exception ex)
            {
                LogError(MethodBase.GetCurrentMethod().Name + "()", ex);
                return false;
            }
        }

        //[Start]v1.0.0.107-1
        void EHC35GlassOutForBridge_OFF(object subjet, System.Timers.ElapsedEventArgs e)
        {
            string _s = string.Empty;
            string _ss = string.Empty;
            try
            {
                UserTimer timer = subjet as UserTimer;
                object[] _objs = (object[])timer.State;

                string _nodeNo = (string)_objs[0];
                string _trxName = (string)_objs[1];
                string _trcaeKey = (string)_objs[2];

                _ss = Helper.GetLogTitle(_nodeNo, _trcaeKey);

                Trx _trx = GetPLCSyncReadTrx(_nodeNo, _trxName.Replace(string.Format("{0}_", _nodeNo), ""));
                if (_trx.NullOrEmpty())
                {
                    _s = Helper.GetDesc(Globals.ReturnCode.Common.RC_TRX_NOT_FOUND, new object[] { string.Format("{0}_{1}", _nodeNo, _trxName.Replace(string.Format("{0}_", _nodeNo), "")) }, true);
                    this.Logger.LogErrorWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", string.Format("{0}{1}", _ss, _s));
                    return;
                }

                if ((_trx.EventGroups[0].Events[0].Items[0].Value.Trim() != string.Empty) || (_trx.EventGroups[0].Events[0].Items[1].Value != "0"))
                {
                    _trx.ClearTrxWith0();
                    SendPLCData(_trx, _trcaeKey);
                    _s = string.Format("TrxName<{0}>, TraceKey<{1}>: The reply Data Is Exist, So it will clear.", _trxName, _trcaeKey);
                    this.Logger.LogErrorWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", string.Format("{0}{1}", _ss, _s));
                }
            }
            catch (Exception ex)
            {
                LogError(MethodBase.GetCurrentMethod().Name + "()", ex);
            }
        }
        //[End]v1.0.0.107-1
        #endregion





        public void EG01GlassBrokenDetectReportBlock(Trx _inputData) {
            string _s = string.Empty;
            try {
                WriteEvtTrxLog(_inputData, MethodBase.GetCurrentMethod().Name);

                // 註解 : CF8A-C#Migration - 修改CF8A拆解資料方法.- Merge後 - (0210)
                RawData_EG01GlassBrokenDetectReportBlock EG01 = new RawData_EG01GlassBrokenDetectReportBlock(_inputData);

                //string _sGlassNumber = _inputData.EventGroups[0].Events[0].Items["GlassNumber"].Value.ToUpperTrim();
                //string _sCornerborkendetect = _inputData.EventGroups[0].Events[0].Items["Glasscornerbrokendetect"].Value.Trim();
                //string _sEdgeBorkenDetect = _inputData.EventGroups[0].Events[0].Items["Glassedgebrokendetect"].Value.Trim();
                //string _sUnitNum = _inputData.EventGroups[0].Events[0].Items["UnitNumber"].Value.Trim();

                string _sGlassNumber = EG01._raw_W_glassnumber;
                string _sCornerborkendetect = EG01._raw_W_glasscornerbrokendetect;
                string _sEdgeBorkenDetect = EG01._raw_W_glassedgebrokendetect;
                string _sUnitNum = EG01._raw_W_unitnumber;
                //end
                Tuple<string, string> _tmp = Tuple.Create(_sCornerborkendetect, _sUnitNum); // 註解 : CF8A-C#Migration.

                eOnOff triggerBit = (eOnOff)int.Parse(_inputData.EventGroups[0].Events[1].Items[0].Value);

                _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                _s += string.Format("GlassNumber<{0}>, Glasscornerbrokendetect<{1}>, Glassedgebrokendetect<{2}>, UnitNumber<{3}>, EG01GlassBrokenDefectReportRequestBlock_Bit<{4}>.", _sGlassNumber, _sCornerborkendetect, _sEdgeBorkenDetect, _sUnitNum, triggerBit);
                this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);

                if (_inputData.IsInitTrigger) return;

                if (triggerBit == eOnOff.ON) {
                    Equipment eqp = ObjectManager.EquipmentManager.GetEQP(_inputData.Metadata.NodeNo);
                    Job job = ObjectManager.JobManager.GetJobByNo(_sGlassNumber);

                    if (eqp == null) {
                        _s = string.Empty;
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                        _s += "BCS NOT FOUND EQUIPMENT OBJECT.";
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                        return;
                    }

                    Line _line = ObjectManager.LineManager.GetLine(Workbench.ServerName);
                    if (_line == null)
                    {
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Receive);
                        _s += Helper.GetDesc(Globals.ReturnCode.Common.RC_LINE_NOT_FOUND, new object[] { Workbench.ServerName }, true);
                        this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                        return;
                    }

                    if (job == null)
                    {
                        //找不到glass記log
                        LogError(MethodBase.GetCurrentMethod().Name + "()", string.Format("Can not find glass wip. Glass Number<[0]>", _sGlassNumber));
                        SendOPITerminalMessage("Glas sBroken DetectReport Block HandShaking Error", string.Format("Can not find glass wip. Glass Number<[0]>", _sGlassNumber));
                    }
                    else
                    {
                        _s = string.Empty;
                        _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey);
                        _s += string.Format("EQ Report: GlassNo=<{0}>, Corner Borken Detect= <{1}>, Detect Result=<{2}>, UnitNum=<{3}>", job._GlassNumberHex, _sCornerborkendetect, _sEdgeBorkenDetect, _sUnitNum);
                        this.Logger.LogInfoWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);

                        Cassette _cst = job._SourceCassette.NullOrEmpty() ? null : job._SourceCassette;//v1.********
                        if (_cst == null)
                        {
                            _s = Helper.GetLogTitle(_inputData.Metadata.NodeNo, _inputData.TrackKey, Helper.eLogTitleDirection.Receive);
                            _s += _s += MISC.Helper.GetDesc(Globals.ReturnCode.Common.RC_CST_NOT_FOUND_BY_ID, new object[] { job._SourceCassette.NullOrEmpty() ? string.Empty : job._SourceCassette._CassetteID }, true);
                            this.Logger.LogWarnWrite(this.LogName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                        }

                        // '4.1.0.207  2013-01-11 maurice  (巡邊檢)Image & GlassID mapping
                        //'(ex. LineIDNodeID_UnitID_GlassID_Date/Time_serial number.png)
                        //'  File name: CNVR0512_13_F8313044CD_20120104103090_1.png
                        //'  Path: 20130104/ CNVR0512/ filename, OPI TODO
                        string _strImageFileName = string.Format("{0}_{1}_{2}_{3}", eqp.Data.NODEID, string.Format("{0:D2}", _sUnitNum), job._JobInfo._GlassID, DateTime.Now.ToString("YYYYMMDDHHmmss"));
                        string _strUnitName = string.Format("{0}-{1}", eqp.Data.NODENAME, _sUnitNum);


                        //BPCSSCRP_I
                        if (_line.File.HostMode != eHostMode.OFFLINE)
                        {
                            Invoke(Globals.ServiceName.MESService, "GlassBrokenDetectReport", new object[] { _line, eqp, _cst, job, _sGlassNumber, _sCornerborkendetect, _sEdgeBorkenDetect, _sUnitNum, _strImageFileName, _inputData.TrackKey });
                        }

                        lock(job)
                        {
                            job.RemoveFlag = true;
                            ObjectManager.JobManager.EnqueueSave(job);
                        }

                        //[Start] v1.0.0.120-7
                        //[Start] v********-8
                        //Log Data TODO
                        // 註解 : CF8A-C#Migration.
                        //ObjectManager.HistoryManager.Record_SHIS_GlassDataErase(eqp, _line, _cst, job, MethodBase.GetCurrentMethod().Name, _inputData);
                        ObjectManager.HistoryManager.Record_SHIS_GlassDataErase(eqp, _line, _cst, job, MethodBase.GetCurrentMethod().Name, _inputData, _tmp);
                        //end

                        GlassBrokenEntity glsBroken = ObjectManager.GlassBrokenManager.GetGlassBroken(_sGlassNumber);
                        if (!glsBroken.NullOrEmpty())
                        {
                            lock (glsBroken)
                            {
                                glsBroken._GlassNo = _sGlassNumber;
                                glsBroken._Cornerborkendetect = _sCornerborkendetect;
                                glsBroken._EdgeBorkenDetect = _sEdgeBorkenDetect;
                                glsBroken._UnitNum = _sUnitNum;

                                ObjectManager.GlassBrokenManager.EnqueueSave(glsBroken);
                            }
                        }
                        else
                        {
                            glsBroken = new GlassBrokenEntity();

                            glsBroken._GlassNo = _sGlassNumber;
                            glsBroken._Cornerborkendetect = _sCornerborkendetect;
                            glsBroken._EdgeBorkenDetect = _sEdgeBorkenDetect;
                            glsBroken._UnitNum = _sUnitNum;

                            ObjectManager.GlassBrokenManager.AddGlassBroken(glsBroken);
                        }
                        //[End] v********-8
                        //[End] v1.0.0.120-7
                    }
                }

            } catch (Exception ex) {
                LogError(MethodBase.GetCurrentMethod().Name + "()", ex);
            }
        }















        #region 舊廠functions, 目前pending!
        public void GlassTurnNormal(Trx inputData) {
            try {
                WriteEvtTrxLog(inputData, MethodBase.GetCurrentMethod().Name);
                if (inputData.IsInitTrigger) return;






            } catch (Exception ex) {
                LogError(MethodBase.GetCurrentMethod().Name + "()", ex);
            }
        }
        public void GlassTurnShort(Trx inputData) {
            try {
                WriteEvtTrxLog(inputData, MethodBase.GetCurrentMethod().Name);
                if (inputData.IsInitTrigger) return;






            } catch (Exception ex) {
                LogError(MethodBase.GetCurrentMethod().Name + "()", ex);
            }
        }
        public void GlassTurnLong(Trx inputData) {
            try {
                WriteEvtTrxLog(inputData, MethodBase.GetCurrentMethod().Name);
                if (inputData.IsInitTrigger) return;






            } catch (Exception ex) {
                LogError(MethodBase.GetCurrentMethod().Name + "()", ex);
            }
        }
        #endregion




    }
}
