﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="nlog" type="NLog.Config.ConfigSection<PERSON>and<PERSON>, NLog" />
  </configSections>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0" />
  </startup>
  <nlog autoReload="true" throwExceptions="true">
    <targets async="true">
      <target name="WriteTo" type="File" deleteOldFileOnStartup="false" layout="${longdate}[${pad:padding=-5:inner=${level:uppercase=true}}]${message}" fileName="D:/UnicomLog/INSP0100/${logger}/${date:format=yyyyMMdd}/${level}/${logger}_${level}_${date:format=HH}.log" archiveFileName="D:/UnicomLog/INSP0100/${logger}/${date:format=yyyyMMdd}/${level}/${logger}_${level}_${date:format=HH}.{#####}.log" archiveAboveSize="20971520" archiveNumbering="Sequence" MaxArchiveFiles="50" concurrentWrites="false" keepFileOpen="false" />
      <target name="WriteToInfo" type="File" deleteOldFileOnStartup="false" layout="${longdate}[${pad:padding=-5:inner=${level:uppercase=true}}]${message}" fileName="D:/UnicomLog/INSP0100/${logger}/${date:format=yyyyMMdd}/Info/${logger}_Info_${date:format=HH}.log" archiveFileName="D:/UnicomLog/INSP0100/${logger}/${date:format=yyyyMMdd}/Info/${logger}_Info_${date:format=HH}.{#####}.log" archiveAboveSize="20971520" archiveNumbering="Sequence" MaxArchiveFiles="50" concurrentWrites="false" keepFileOpen="false" />
    </targets>
    <rules>
      <logger name="CORE" levels="Error" writeTo="WriteTo" />
      <logger name="CORE" levels="Info,Error,Warn,Debug" writeTo="WriteToInfo" final="true" />
      <logger name="Service" levels="Error,Trace" writeTo="WriteTo" />
      <logger name="Service" levels="Error,Debug,Warn,Info" writeTo="WriteToInfo" final="true" />
      <logger name="PerformanceService" levels="Error" writeTo="WriteTo" />
      <logger name="PerformanceService" levels="Error,Debug,Warn,Info" writeTo="WriteToInfo" final="true" />
      <logger name="PLCAgent" levels="Info,Error,Trace" writeTo="WriteTo" />
      <logger name="PLCAgent" levels="Debug,Warn,Error" writeTo="WriteToInfo" final="true" />
      <logger name="OPIAgent" levels="Info,Error,Trace" writeTo="WriteTo" />
      <logger name="OPIAgent" levels="Debug,Warn,Error" writeTo="WriteToInfo" final="true" />
      <logger name="MESAgent" levels="Info,Error,Trace" writeTo="WriteTo" />
      <logger name="MESAgent" levels="Debug,Warn,Error" writeTo="WriteToInfo" final="true" />
      <logger name="DcsActiveSocketAgent" levels="Info,Error,Trace" writeTo="WriteTo" />
      <logger name="DcsActiveSocketAgent" levels="Debug,Warn,Error" writeTo="WriteToInfo" final="true" />
      <logger name="DcsPassiveSocketAgent" levels="Info,Error,Trace" writeTo="WriteTo" />
      <logger name="DcsPassiveSocketAgent" levels="Debug,Warn,Error" writeTo="WriteToInfo" final="true" />
      <logger name="BmsAgent" levels="Info,Error,Trace" writeTo="WriteTo" />
      <logger name="BmsAgent" levels="Debug,Warn,Error" writeTo="WriteToInfo" final="true" />
    </rules>
  </nlog>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <probing privatePath=".;dll" />
    </assemblyBinding>
  </runtime>
  <appSettings>
    <!--监测间隔时间(区间：10分钟(600000)~2小时(7200000))-->
    <add key="MonitorInterval" value="600000" />
    <!--多个应用程式名称用逗号隔开-->
    <add key="MonitorProcessorName" value="UniBCS,UniOPI,sqlservr,winvnc" />
    <add key="UserName" value="CIM" />
    <add key="Password" value="123456" />
    <add key="ClientSettingsProvider.ServiceUri" value="" />
  </appSettings>
  <system.web>
    <membership defaultProvider="ClientAuthenticationMembershipProvider">
      <providers>
        <add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" />
      </providers>
    </membership>
    <roleManager defaultProvider="ClientRoleProvider" enabled="true">
      <providers>
        <add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400" />
      </providers>
    </roleManager>
  </system.web>
</configuration>